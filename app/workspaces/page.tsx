'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { Button, Typography, Tag, Alert, Empty } from 'antd';
import SpinLoading from '@/app/components/loading/SpinLoading';
import { PlusOutlined, RightOutlined } from '@ant-design/icons';
import { getWorkspacesWithUserCount } from '@/app/actions/workspace';
import Link from 'next/link';
import Header from '@/app/components/Header';

const { Text } = Typography;

interface WorkspaceWithRole {
  id: string;
  name: string;
  owner: string | null;
  plan: 'free' | 'plus' | 'pro';
  role: 'owner' | 'admin' | 'member';
  userCount: number;
  createdAt: Date;
  updatedAt: Date;
}

export default function WorkspacesPage() {
  const router = useRouter();
  const { data: session, status } = useSession();
  const [workspaces, setWorkspaces] = useState<WorkspaceWithRole[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (status === 'loading') {
      setLoading(true);
      return;
    }
    console.log('------status--------')
    console.log(status)
    // if (status === 'unauthenticated') {
    //   router.push('/login');
    //   return;
    // }
    if (status === 'authenticated') {
      loadWorkspaces();
    }

    // loadWorkspaces();
  }, [session, status, router]);

  const loadWorkspaces = async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await getWorkspacesWithUserCount();

      if (result.status === 'success') {
        setWorkspaces(result.data);
      } else {
        setError(result.message || '获取工作区列表失败');
      }
    } catch (err) {
      setError('获取工作区列表时发生错误');
      console.error('Load workspaces error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateWorkspace = () => {
    router.push('/onboarding');
  };

  if (loading) {
    return (
      <main className="h-dvh flex justify-center items-center">
        <SpinLoading />
        <span className='ml-2 text-gray-600 text-sm'>Loading ...</span>
      </main>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Alert
          message="加载失败"
          description={error}
          type="error"
          showIcon
          action={
            <Button size="small" onClick={loadWorkspaces}>
              重试
            </Button>
          }
        />
      </div>
    );
  }

  return (
    <div className="min-h-dvh bg-slate-50">
      {/* Header with Logo and User Info - Full Width */}
      <Header />

      {/* Main Content Area */}
      <div className="container mx-auto px-4 sm:px-6 py-6 sm:py-8 max-w-4xl">

        {/* Page Title and Create Button */}
        {workspaces.length > 0 && <div className="flex justify-between items-center mb-6 sm:mb-8">
          <h2 className="mb-0 text-base sm:text-xl">
            选择工作区
          </h2>
          <Button
            type="default"
            icon={<PlusOutlined />}
            onClick={handleCreateWorkspace}
            size="middle"
          >
            <span className="hidden sm:inline">创建</span>
            <span className="sm:hidden">+</span>
          </Button>
        </div>}

        {/* Workspaces List */}
        <div>
          {workspaces.length === 0 ? (
            <div className="text-center py-16">
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description={
                  <span>
                    您还没有加入任何工作区
                    <br />
                    创建一个新的工作区开始使用
                  </span>
                }
              >
                <Button type="primary" icon={<PlusOutlined />} onClick={handleCreateWorkspace}>
                  创建工作区
                </Button>
              </Empty>
            </div>
          ) : (
            <div>
              {workspaces.map((workspace) => (
                <Link key={workspace.id} href={`/${workspace.id}/chat`}>
                  <div
                    className="workspace-item bg-white border border-gray-200 rounded-lg overflow-hidden cursor-pointer mt-4 hover:bg-gray-50 transition-colors duration-200 px-4 sm:px-6 py-3 sm:py-4"
                  >
                    <div className="flex justify-between items-center w-full">
                      <div className="flex-1 min-w-0">
                        <div className="text-sm sm:text-base font-medium text-gray-600 truncate">
                          {workspace.name}
                        </div>
                      </div>
                      <div className="flex items-center space-x-1 sm:space-x-2 text-gray-400 flex-shrink-0">
                        <span className="text-xs">
                          {workspace.role === 'owner' ? <Tag color="blue" bordered={false}>所有者</Tag> :
                            workspace.role === 'admin' ? <Tag color="blue" bordered={false}>管理员</Tag> : <Tag>成员</Tag>}
                        </span>
                        <span className="text-xs sm:text-sm">
                          {workspace.userCount} 个用户
                        </span>
                        <RightOutlined className="text-xs" />
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}